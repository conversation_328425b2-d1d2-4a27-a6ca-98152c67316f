{"systemAssumptions": {"application": "智能手机水下无线声通信", "device": "智能手机 (通用扬声器/麦克风)", "transmitPower": "低且恒定 (受限于手机扬声器)", "channelModel": "水下声学信道 (UAC)", "rate_range_bps": {"min": 300, "max": 1300}, "modulation_assumption": "QPSK (或类似的抗噪调制方式)", "description": "针对特定水下声通信场景的参数假设"}, "analysisScenarios": [{"scenarioID": "Scenario 1", "title": "速率 vs. 相对比特能量 (Rate vs. Relative Bit Energy)", "description": "展示了降低通信速率如何集中能量到每个比特上。能量以最低速率(300bps)时的能量为基准'1.0'进行归一化。", "data": [{"rate_bps": 1300, "relative_bit_energy": 0.23}, {"rate_bps": 1000, "relative_bit_energy": 0.3}, {"rate_bps": 800, "relative_bit_energy": 0.38}, {"rate_bps": 500, "relative_bit_energy": 0.6}, {"rate_bps": 300, "relative_bit_energy": 1.0}]}, {"scenarioID": "Scenario 2", "title": "速率 vs. 预估误码率 (Rate vs. Estimated Bit Error Rate)", "description": "预估在水下声学信道中，不同通信速率对应的误码率。反映了牺牲速率以换取通信可靠性的核心策略。", "data": [{"rate_bps": 1300, "scenario_description": "最高速率，抗干扰能力最弱", "estimated_eb_n0_dB": 3.0, "estimated_ber": "1.9e-2"}, {"rate_bps": 1000, "scenario_description": "速率较高，连接不稳定", "estimated_eb_n0_dB": 5.0, "estimated_ber": "7.8e-3"}, {"rate_bps": 800, "scenario_description": "速率中等，性能有所改善", "estimated_eb_n0_dB": 7.0, "estimated_ber": "1.1e-3"}, {"rate_bps": 500, "scenario_description": "速率较低，连接较为可靠", "estimated_eb_n0_dB": 10.0, "estimated_ber": "3.9e-6"}, {"rate_bps": 300, "scenario_description": "最低速率，最为稳健可靠", "estimated_eb_n0_dB": 12.0, "estimated_ber": "1.5e-8"}]}]}