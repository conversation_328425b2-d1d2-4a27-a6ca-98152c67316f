#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SNR-BER-符号持续时间三参数分析
SNR-BER-Symbol Duration Three-Parameter Analysis
"""

import json
import numpy as np
import matplotlib.pyplot as plt
from scipy.interpolate import CubicSpline
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
matplotlib.rcParams['axes.unicode_minus'] = False

def load_json6_data():
    """加载6.json数据"""
    import os
    current_dir = os.path.dirname(os.path.abspath(__file__))
    json_path = os.path.join(current_dir, '6.json')
    with open(json_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    return data

def extract_performance_data():
    """提取性能数据"""
    data = load_json6_data()
    scenario = data['performanceScenarios'][0]  # 场景B
    results = scenario['results']
    
    # 提取数据点
    symbol_durations = [result['symbol_duration_s'] for result in results]
    es_n0_values = [result['resulting_es_n0_dB'] for result in results]
    ber_values = [float(result['final_ber']) for result in results]
    analyses = [result['analysis'] for result in results]
    
    return np.array(symbol_durations), np.array(es_n0_values), np.array(ber_values), analyses

def estimate_snr_values(durations, es_n0_values, ber_values):
    """基于Es/N0和BER推断SNR值"""
    snr_values = []
    for dur, es_n0, ber in zip(durations, es_n0_values, ber_values):
        # 基于符号持续时间和Es/N0推断SNR
        symbol_rate = 1.0 / dur  # 符号速率
        processing_gain = 10 * np.log10(symbol_rate / 1.0)  # 处理增益

        # SNR推断：考虑带宽和处理增益
        snr = es_n0 - processing_gain - 3  # 经验调整
        snr_values.append(snr)

    return np.array(snr_values)

def plot_snr_ber_duration_analysis():
    """绘制SNR、BER、符号持续时间三参数分析图"""
    durations, es_n0, ber_values, analyses = extract_performance_data()
    
    # 推断SNR值
    snr_values = estimate_snr_values(durations, es_n0, ber_values)
    
    # 创建密集的插值点
    duration_smooth = np.linspace(0.1, 1.5, 500)
    
    # SNR插值
    cs_snr = CubicSpline(durations, snr_values)
    snr_smooth = cs_snr(duration_smooth)
    
    # BER插值（对数空间）
    cs_ber = CubicSpline(durations, np.log10(ber_values))
    ber_smooth = 10**cs_ber(duration_smooth)
    
    # 创建双轴图
    fig, ax1 = plt.subplots(figsize=(16, 10))
    
    # 左轴：SNR
    color1 = '#2E8B57'  # 海绿色
    ax1.set_xlabel('符号持续时间 (s)', fontsize=16, fontweight='bold')
    ax1.set_ylabel('信噪比 SNR (dB)', color=color1, fontsize=16, fontweight='bold')
    
    # 绘制SNR连续曲线
    line1 = ax1.plot(duration_smooth, snr_smooth, color=color1, linewidth=4, 
                     alpha=0.8, label='SNR连续曲线')
    
    # 绘制SNR原始数据点
    snr_points = ax1.plot(durations, snr_values, 'o', color=color1, markersize=12, 
                         markerfacecolor='lightgreen', markeredgecolor=color1, 
                         markeredgewidth=3, label='SNR数据点')
    
    ax1.tick_params(axis='y', labelcolor=color1, labelsize=12)
    ax1.grid(True, alpha=0.3)
    ax1.set_ylim(-2, 12)
    
    # 右轴：BER
    ax2 = ax1.twinx()
    color2 = '#DC143C'  # 深红色
    ax2.set_ylabel('误码率 BER', color=color2, fontsize=16, fontweight='bold')
    
    # 绘制BER连续曲线
    line2 = ax2.semilogy(duration_smooth, ber_smooth, color=color2, linewidth=4, 
                         alpha=0.8, label='BER连续曲线')
    
    # 绘制BER原始数据点
    ber_points = ax2.semilogy(durations, ber_values, 's', color=color2, markersize=12,
                             markerfacecolor='lightcoral', markeredgecolor=color2, 
                             markeredgewidth=3, label='BER数据点')
    
    ax2.tick_params(axis='y', labelcolor=color2, labelsize=12)
    ax2.set_ylim(1e-6, 1e-1)
    
    # 只标注符号时间（简化版）
    for i, (dur, snr, ber) in enumerate(zip(durations, snr_values, ber_values)):
        # 只在数据点下方标注符号时间
        ax1.annotate(f'{dur}s', xy=(dur, snr), xytext=(0, -25),
                    textcoords='offset points', fontsize=12, fontweight='bold',
                    ha='center', va='top')
    
    # 标注信道相干时间
    coherence_line = ax1.axvline(x=0.8, color='purple', linestyle='--', linewidth=3, alpha=0.8)
    
    # 添加性能区域
    ax1.fill_betweenx(ax1.get_ylim(), 0, 0.8, alpha=0.15, color='green', label='信道稳定区域')
    ax1.fill_betweenx(ax1.get_ylim(), 0.8, 1.5, alpha=0.15, color='red', label='信道老化区域')
    
    # 标注最优点（简化版）
    optimal_idx = np.argmin(ber_values)
    ax1.annotate('最优点',
                xy=(durations[optimal_idx], snr_values[optimal_idx]),
                xytext=(0.3, 8), fontsize=14, fontweight='bold', color='darkblue',
                arrowprops=dict(arrowstyle='->', color='darkblue', lw=3),
                bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.9))
    
    # 简化图例
    lines1 = line1 + snr_points
    lines2 = line2 + ber_points
    coherence_legend = [plt.Line2D([0], [0], color='purple', linestyle='--', linewidth=3)]

    plt.tight_layout()
    plt.savefig('snr_ber_duration_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()  # 关闭图表而不显示
    
    print("SNR-BER-符号持续时间分析图已生成：snr_ber_duration_analysis.png")
    
    # 打印推断的SNR值
    print(f"\n推断的SNR值:")
    for dur, snr, es_n0 in zip(durations, snr_values, es_n0):
        print(f"   • {dur}s: SNR={snr:.1f}dB (Es/N0={es_n0}dB)")
    
    print(f"\n关键发现:")
    print(f"   • SNR随符号时间变化：{snr_values[0]:.1f}dB → {snr_values[-1]:.1f}dB")
    print(f"   • BER最优点在{durations[optimal_idx]}s处")
    print(f"   • 信道相干时间0.8s是性能分界点")
    
    return snr_values

if __name__ == "__main__":
    print("正在生成SNR-BER-符号持续时间三参数分析图...")
    plot_snr_ber_duration_analysis()
    print("完成！")
