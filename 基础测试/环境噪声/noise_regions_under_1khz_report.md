# 6个噪声区域 1000Hz以下频段详细分析报告

## 📊 分析概览

**分析频段**: 0-1000 Hz (低频段详细分析)  
**区域数量**: 6个不同环境噪声区域  
**分析精度**: 13个细分频段 (50Hz间隔)  
**分析重点**: 低频噪声特征、主导频率、能量分布  

---

## 🎯 核心发现

### 🔥 **主要频率特征**
- **200Hz绝对主导**: 5个区域都以200Hz为主峰
- **100Hz独特主导**: 区域6是唯一以100Hz为主峰的区域
- **频率精度极高**: 主峰频率在199.7-200.2Hz之间，变化仅0.5Hz
- **低频集中**: 主要能量集中在150-250Hz频段

### 📈 **复杂度差异巨大**
- **最复杂**: 区域2 (2059个峰值) - 频谱最丰富
- **最简单**: 区域1 (97个峰值) - 频谱最纯净
- **复杂度比例**: 相差**21倍**！

---

## 📊 区域详细对比

### 🏷️ **区域汇总表**

| 区域 | 文件名 | 主峰频率 | 频谱质心 | 峰值数 | RMS幅度 | 频谱带宽 | 特征 |
|------|--------|----------|----------|--------|---------|----------|------|
| **区域1** | 2区域噪声检测.wav | **199.9Hz** | 382.5Hz | **97** | 0.058427 | 273.1Hz | 🔇 **最简单** |
| **区域2** | recording_144612.wav | **200.2Hz** | 491.0Hz | **2059** | 0.052871 | 264.4Hz | 🔥 **最复杂** |
| **区域3** | recording_144415.wav | **200.1Hz** | 463.1Hz | **1580** | 0.054651 | 246.2Hz | 📈 高复杂度 |
| **区域4** | recording_144210.wav | **199.7Hz** | 404.3Hz | **1230** | **0.069749** | 252.7Hz | 🔊 **最嘈杂** |
| **区域5** | recording_144658.wav | **200.1Hz** | 396.4Hz | **268** | **0.024889** | 244.2Hz | 🔇 **最安静** |
| **区域6** | recording_144528.wav | **100.1Hz** | 497.0Hz | **1962** | 0.047154 | 260.0Hz | ⭐ **独特低频** |

---

## 🔍 详细频段分析

### 📈 **150-250Hz主导频段分析**

| 区域 | 150-200Hz能量% | 200-250Hz能量% | 合计能量% | 主导特征 |
|------|---------------|---------------|-----------|----------|
| **区域1** | **69.10%** | 6.96% | **76.06%** | 🔥 **200Hz绝对主导** |
| **区域5** | 29.35% | **46.06%** | **75.41%** | 🔥 **200Hz绝对主导** |
| **区域4** | 21.45% | 10.78% | **32.23%** | 📈 200Hz主导但分散 |
| **区域3** | 6.73% | **19.61%** | **26.34%** | 📈 200Hz主导但分散 |
| **区域2** | 16.50% | **18.77%** | **35.27%** | 📈 200Hz主导但分散 |
| **区域6** | 10.60% | 10.30% | **20.90%** | 📉 100Hz主导，200Hz次要 |

### 🎵 **50-150Hz低频段分析**

| 区域 | 50-100Hz能量% | 100-150Hz能量% | 合计能量% | 低频特征 |
|------|--------------|---------------|-----------|----------|
| **区域6** | 4.53% | **12.01%** | **16.54%** | 🔥 **100Hz主导** |
| **区域4** | **10.88%** | **12.65%** | **23.53%** | 📈 低频丰富 |
| **区域3** | 5.12% | 6.42% | **11.54%** | 📈 中等低频 |
| **区域2** | 3.65% | 8.65% | **12.30%** | 📈 中等低频 |
| **区域1** | 7.27% | 5.54% | **12.81%** | 📈 中等低频 |
| **区域5** | 1.64% | 4.18% | **5.82%** | 📉 低频最少 |

---

## 🎯 区域特征分类

### 🔥 **主导频率模式分类**

#### 📈 **200Hz绝对主导型** (区域1, 5)
- **特征**: 150-250Hz能量占75%以上
- **区域1**: 69.10% + 6.96% = 76.06%
- **区域5**: 29.35% + 46.06% = 75.41%
- **应用**: 基准噪声环境，频谱纯净

#### 📊 **200Hz主导分散型** (区域2, 3, 4)
- **特征**: 200Hz主导但能量分散到其他频段
- **区域4**: 32.23% (最分散)
- **区域2**: 35.27% (中等分散)
- **区域3**: 26.34% (较分散)
- **应用**: 复杂噪声环境

#### ⭐ **100Hz独特主导型** (区域6)
- **特征**: 唯一以100Hz为主峰的区域
- **100-150Hz**: 12.01% (最高)
- **200Hz**: 仍有10.30%的次要能量
- **应用**: 低频设备噪声环境

### 🔊 **复杂度分类**

#### 🔇 **简单型** (峰值<500)
- **区域1**: 97个峰值 - 极简单
- **区域5**: 268个峰值 - 简单

#### 📈 **复杂型** (峰值1000-2000)
- **区域3**: 1580个峰值
- **区域4**: 1230个峰值
- **区域6**: 1962个峰值

#### 🔥 **极复杂型** (峰值>2000)
- **区域2**: 2059个峰值 - 最复杂

---

## 📊 能量分布热力图分析

### 🔥 **高能量频段** (>15%能量)
- **150-200Hz**: 区域1 (69.10%) 🔥🔥🔥
- **200-250Hz**: 区域5 (46.06%) 🔥🔥
- **200-250Hz**: 区域3 (19.61%) 🔥
- **200-250Hz**: 区域2 (18.77%) 🔥
- **500-600Hz**: 区域4 (15.93%) 🔥

### 📈 **中等能量频段** (10-15%能量)
- **100-150Hz**: 区域6 (12.01%), 区域4 (12.65%)
- **600-700Hz**: 区域2 (12.96%), 区域3 (12.43%)
- **700-800Hz**: 区域6 (14.81%)

### 📉 **低能量频段** (<5%能量)
- **0-50Hz**: 所有区域都<1%
- **900-1000Hz**: 所有区域都<3%

---

## 🔬 技术特征对比

### ⚡ **频谱质心分析**
- **最高**: 区域6 (497.0Hz) - 能量向高频偏移
- **最低**: 区域1 (382.5Hz) - 能量集中在低频
- **差异**: 114.5Hz的质心偏移

### 📏 **频谱带宽分析**
- **最宽**: 区域1 (273.1Hz) - 频谱分散
- **最窄**: 区域5 (244.2Hz) - 频谱集中
- **差异**: 28.9Hz的带宽差异

### 🔊 **噪声强度排序**
1. **区域4**: 0.069749 (最强)
2. **区域1**: 0.058427
3. **区域3**: 0.054651
4. **区域2**: 0.052871
5. **区域6**: 0.047154
6. **区域5**: 0.024889 (最弱)

---

## 🎯 应用建议

### 🔍 **环境分类应用**
1. **基准环境**: 区域1, 5 (200Hz纯净主导)
2. **复杂环境**: 区域2, 3, 4 (多频成分)
3. **特殊环境**: 区域6 (100Hz主导)

### 🛠️ **技术应用建议**

#### 📊 **滤波设计**
- **主要陷波**: 200Hz (5个区域共同)
- **次要陷波**: 100Hz (区域6特有)
- **保护频段**: 250-400Hz (相对清洁)

#### 🔧 **信号检测**
- **避开频段**: 150-250Hz (主要干扰)
- **推荐频段**: 300-500Hz (中等干净)
- **备选频段**: 800-1000Hz (能量最低)

#### 📈 **环境监测**
- **噪声指标**: 200Hz幅度
- **复杂度指标**: 峰值数量
- **环境类型**: 频谱质心位置

---

## 💡 关键结论

### ✅ **主要发现**
1. **200Hz是绝对主导频率** - 5/6区域的主峰
2. **复杂度差异极大** - 峰值数相差21倍
3. **能量高度集中** - 150-250Hz占主要能量
4. **区域6独特性** - 唯一的100Hz主导

### 🎯 **缩小到1000Hz的价值**
- **细节更清晰**: 13个细分频段vs原来的7个
- **差异更明显**: 低频特征差异被放大
- **应用更精确**: 针对性的低频噪声控制

### 🔧 **实用价值**
- **噪声控制**: 精确的低频滤波设计
- **环境分类**: 基于低频特征的环境识别
- **设备诊断**: 100Hz vs 200Hz主导的设备状态

---

*1000Hz以下频段专项分析 - 2025-06-24*  
*重点: 低频噪声特征、主导频率、精细频段分析*
