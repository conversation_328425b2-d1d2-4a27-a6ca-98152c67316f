# 环境噪声分析报告

## 📋 分析概览

**分析时间**: 2025-06-24  
**分析文件数**: 6个wav文件  
**文件来源**: 水下/环境噪声 文件夹  
**分析频率范围**: 0-5000 Hz  

---

## 📊 文件汇总对比

| 文件名 | 时长(秒) | 采样率 | 峰值数 | 频谱质心(Hz) | RMS幅度 | 特征 |
|--------|----------|--------|--------|--------------|---------|------|
| **2区域噪声检测.wav** | 50.22 | 48000 | 6 | 1360.3 | 0.058427 | 🔇 峰值最少 |
| **recording_20250624_144612.wav** | 35.12 | 48000 | 167 | 664.9 | 0.052871 | 📈 中等复杂度 |
| **recording_20250624_144415.wav** | 42.92 | 48000 | 28 | 957.0 | 0.054651 | 📊 低复杂度 |
| **recording_20250624_144210.wav** | 10.08 | 48000 | 568 | 1099.2 | 0.069749 | 🔥 **峰值最多** |
| **recording_20250624_144658.wav** | 42.40 | 48000 | 23 | 593.8 | 0.024889 | 🔇 **最安静** |
| **recording_20250624_144528.wav** | 35.44 | 48000 | 231 | 770.6 | 0.047154 | 📈 中等复杂度 |

---

## 🎯 关键发现

### 🔊 噪声强度排序 (按RMS幅度)
1. **recording_20250624_144210.wav** - 0.069749 (最强)
2. **2区域噪声检测.wav** - 0.058427
3. **recording_20250624_144415.wav** - 0.054651
4. **recording_20250624_144612.wav** - 0.052871
5. **recording_20250624_144528.wav** - 0.047154
6. **recording_20250624_144658.wav** - 0.024889 (最弱)

### 🎵 频谱复杂度排序 (按峰值数量)
1. **recording_20250624_144210.wav** - 568个峰值 (最复杂)
2. **recording_20250624_144528.wav** - 231个峰值
3. **recording_20250624_144612.wav** - 167个峰值
4. **recording_20250624_144415.wav** - 28个峰值
5. **recording_20250624_144658.wav** - 23个峰值
6. **2区域噪声检测.wav** - 6个峰值 (最简单)

---

## 📈 详细文件分析

### 1. 2区域噪声检测.wav
**特征**: 低复杂度、高频谱质心

| 参数 | 值 |
|------|-----|
| **主导频率** | 199.9 Hz (归一化幅度: 1.000) |
| **频谱质心** | 1360.3 Hz |
| **主要能量分布** | 100-500Hz (70.41%) |
| **噪声类型** | 单一频率主导的环境噪声 |

**主要峰值**:
- 199.9 Hz (主峰)
- 99.9 Hz (谐波)
- 165.1 Hz (次要峰值)

### 2. recording_20250624_144612.wav
**特征**: 中等复杂度、低频主导

| 参数 | 值 |
|------|-----|
| **主导频率** | 200.2 Hz (归一化幅度: 1.000) |
| **频谱质心** | 664.9 Hz |
| **主要能量分布** | 100-500Hz (52.89%), 500-1000Hz (41.63%) |
| **噪声类型** | 宽频带环境噪声 |

**主要峰值**:
- 200.2 Hz (主峰)
- 165.3 Hz (强次峰)
- 100.1 Hz (谐波)

### 3. recording_20250624_144415.wav
**特征**: 低复杂度、中频主导

| 参数 | 值 |
|------|-----|
| **主导频率** | 200.1 Hz (归一化幅度: 1.000) |
| **频谱质心** | 957.0 Hz |
| **主要能量分布** | 100-500Hz (57.07%), 500-1000Hz (34.11%) |
| **噪声类型** | 双频段分布噪声 |

**主要峰值**:
- 200.1 Hz (主峰)
- 608.8 Hz (中频峰值)
- 68.3 Hz (低频成分)

### 4. recording_20250624_144210.wav ⭐ **最复杂**
**特征**: 高复杂度、多峰值、强噪声

| 参数 | 值 |
|------|-----|
| **主导频率** | 199.7 Hz (归一化幅度: 1.000) |
| **频谱质心** | 1099.2 Hz |
| **主要能量分布** | 100-500Hz (54.41%), 500-1000Hz (23.10%) |
| **噪声类型** | 复杂多频环境噪声 |

**主要峰值**:
- 199.7 Hz (主峰)
- 555.4 Hz (中频强峰)
- 92.1 Hz (低频强峰)

### 5. recording_20250624_144658.wav ⭐ **最安静**
**特征**: 低噪声、简单频谱

| 参数 | 值 |
|------|-----|
| **主导频率** | 200.1 Hz (归一化幅度: 1.000) |
| **频谱质心** | 593.8 Hz |
| **主要能量分布** | 100-500Hz (86.00%) |
| **噪声类型** | 单频段主导的安静环境 |

**主要峰值**:
- 200.1 Hz (绝对主峰)
- 165.3 Hz (次峰)
- 200.5 Hz (主峰附近)

### 6. recording_20250624_144528.wav
**特征**: 中高复杂度、平衡分布

| 参数 | 值 |
|------|-----|
| **主导频率** | 100.1 Hz (归一化幅度: 1.000) |
| **频谱质心** | 770.6 Hz |
| **主要能量分布** | 500-1000Hz (47.51%), 100-500Hz (44.98%) |
| **噪声类型** | 平衡双频段噪声 |

**主要峰值**:
- 100.1 Hz (主峰)
- 200.2 Hz (强次峰)
- 165.3 Hz (中等峰值)

---

## 🔍 频段能量分析

### 各频段平均能量分布

| 频段 | 平均能量% | 主要特征 |
|------|-----------|----------|
| **100-500Hz** | **58.6%** | 🔥 **主要能量区域** |
| **500-1000Hz** | **26.7%** | 📈 次要能量区域 |
| **0-100Hz** | **5.2%** | 📉 低频基础 |
| **1000-2000Hz** | **5.6%** | 📉 中高频成分 |
| **2000-3000Hz** | **0.7%** | 📉 高频微弱 |
| **3000-4000Hz** | **0.2%** | 📉 极高频微弱 |
| **4000-5000Hz** | **0.1%** | 📉 超高频微弱 |

### 频段特征总结
- **主导频段**: 100-500Hz (占总能量的58.6%)
- **次要频段**: 500-1000Hz (占总能量的26.7%)
- **高频衰减**: 2000Hz以上能量急剧衰减
- **低频基础**: 0-100Hz有稳定的基础能量

---

## 🎵 共同特征分析

### 频率模式
1. **200Hz附近主峰**: 所有文件都在200Hz附近有强峰值
2. **100Hz谐波**: 多数文件在100Hz有谐波成分
3. **165Hz次峰**: 多个文件在165Hz有次要峰值
4. **高频衰减**: 所有文件在2000Hz以上能量都很弱

### 噪声类型分类
- **简单型** (峰值<50): 2区域噪声检测.wav, recording_144658.wav, recording_144415.wav
- **复杂型** (峰值>150): recording_144612.wav, recording_144528.wav, recording_144210.wav

### 环境特征推测
- **水下环境**: 低频主导，高频衰减明显
- **机械噪声**: 200Hz附近的强峰值可能来自设备
- **环境变化**: 不同时间录制的文件显示环境噪声的变化

---

## 🔬 技术建议

### 噪声控制建议
1. **重点关注200Hz**: 这是所有环境中的主要噪声源
2. **低频滤波**: 考虑在100-500Hz范围内进行噪声抑制
3. **时间变化监控**: recording_144210.wav显示某些时段噪声显著增强

### 进一步分析建议
1. **时频分析**: 了解噪声的时间变化特性
2. **相关性分析**: 研究不同文件间的噪声相关性
3. **噪声源识别**: 分析200Hz峰值的具体来源

---

*报告生成时间: 2025-06-24*  
*分析工具: Python + librosa + scipy + matplotlib*
