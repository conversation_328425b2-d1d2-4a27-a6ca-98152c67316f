#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
6.json连续插值分析 - 动态真实信道下的性能曲线
JSON6 Continuous Interpolation Analysis - Dynamic Real Channel Performance
"""

import json
import numpy as np
import matplotlib.pyplot as plt
from scipy.interpolate import CubicSpline, interp1d, griddata
from mpl_toolkits.mplot3d import Axes3D
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
matplotlib.rcParams['axes.unicode_minus'] = False

def load_json6_data():
    """加载6.json数据"""
    import os
    current_dir = os.path.dirname(os.path.abspath(__file__))
    json_path = os.path.join(current_dir, '6.json')
    with open(json_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    return data

def extract_performance_data():
    """提取性能数据"""
    data = load_json6_data()
    scenario = data['performanceScenarios'][0]  # 场景B
    results = scenario['results']
    
    # 提取数据点
    symbol_durations = [result['symbol_duration_s'] for result in results]
    es_n0_values = [result['resulting_es_n0_dB'] for result in results]
    ber_values = [float(result['final_ber']) for result in results]
    analyses = [result['analysis'] for result in results]
    
    return np.array(symbol_durations), np.array(es_n0_values), np.array(ber_values), analyses

def plot_continuous_ber_curve():
    """绘制连续BER曲线"""
    durations, es_n0, ber_values, analyses = extract_performance_data()
    
    # 创建密集的插值点
    duration_smooth = np.linspace(0.1, 1.5, 500)
    
    # 使用三次样条插值生成平滑曲线
    cs_ber = CubicSpline(durations, np.log10(ber_values))  # 对BER取对数插值
    ber_smooth = 10**cs_ber(duration_smooth)
    
    # 创建图表
    plt.figure(figsize=(14, 10))
    
    # 绘制连续BER曲线
    plt.semilogy(duration_smooth, ber_smooth, 'b-', linewidth=4, alpha=0.8, 
                 label='连续BER曲线 (三次样条插值)')
    
    # 绘制原始数据点
    colors = ['red', 'green', 'orange', 'purple']
    for i, (dur, ber, analysis, color) in enumerate(zip(durations, ber_values, analyses, colors)):
        plt.semilogy(dur, ber, 'o', markersize=15, color=color, 
                    markeredgecolor='black', markeredgewidth=2, 
                    label=f'{dur}s: {ber:.1e}')
        
        # 添加分析标注
        plt.annotate(f'{dur}s\nBER={ber:.1e}\n{analysis[:15]}...', 
                    xy=(dur, ber), xytext=(dur + 0.1, ber * 3),
                    fontsize=10, ha='left', va='bottom',
                    bbox=dict(boxstyle='round,pad=0.3', facecolor=color, alpha=0.7),
                    arrowprops=dict(arrowstyle='->', color=color, lw=2))
    
    # 标注信道相干时间
    coherence_time = 0.8
    plt.axvline(x=coherence_time, color='red', linestyle='--', linewidth=3, alpha=0.8,
                label=f'信道相干时间 Tc = {coherence_time}s')
    
    # 添加性能区域
    plt.fill_betweenx([1e-6, 1], 0, coherence_time, alpha=0.2, color='green', 
                     label='信道稳定区域')
    plt.fill_betweenx([1e-6, 1], coherence_time, 1.5, alpha=0.2, color='red', 
                     label='信道老化区域')
    
    # 标注最优点
    optimal_idx = np.argmin(ber_values)
    plt.annotate('最优工作点\n(黄金平衡点)', 
                xy=(durations[optimal_idx], ber_values[optimal_idx]),
                xytext=(0.3, 1e-4), fontsize=14, fontweight='bold', color='darkgreen',
                arrowprops=dict(arrowstyle='->', color='darkgreen', lw=3),
                bbox=dict(boxstyle='round,pad=0.5', facecolor='lightgreen', alpha=0.9))
    
    plt.xlabel('符号持续时间 (s)', fontsize=16, fontweight='bold')
    plt.ylabel('误码率 (BER)', fontsize=16, fontweight='bold')
    plt.title('动态真实信道下的BER性能曲线\n(能量增益 vs 信道老化)', fontsize=18, fontweight='bold')
    plt.grid(True, alpha=0.3)
    plt.legend(fontsize=11, loc='upper right')
    plt.xlim(0.05, 1.6)
    plt.ylim(1e-6, 1e-1)
    
    # 添加说明文本
    plt.text(0.02, 0.02, 
             '关键发现:\n• 0.5s达到最优性能\n• 超过Tc=0.8s后性能恶化\n• 信道老化效应压倒能量增益', 
             transform=plt.gca().transAxes, fontsize=12, verticalalignment='bottom',
             bbox=dict(boxstyle='round,pad=0.5', facecolor='lightblue', alpha=0.9))
    
    plt.tight_layout()
    plt.savefig('json6_continuous_ber_curve.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("6.json连续BER曲线已生成：json6_continuous_ber_curve.png")

def plot_es_n0_vs_ber_relationship():
    """绘制Es/N0 vs BER关系的连续曲线"""
    durations, es_n0, ber_values, analyses = extract_performance_data()
    
    # 创建密集的插值点
    es_n0_smooth = np.linspace(np.min(es_n0), np.max(es_n0), 500)
    
    # 使用插值生成平滑的Es/N0 vs BER曲线
    cs_ber_vs_snr = CubicSpline(es_n0, np.log10(ber_values))
    ber_vs_snr_smooth = 10**cs_ber_vs_snr(es_n0_smooth)
    
    plt.figure(figsize=(12, 8))
    
    # 绘制连续曲线
    plt.semilogy(es_n0_smooth, ber_vs_snr_smooth, 'g-', linewidth=4, alpha=0.8,
                 label='连续Es/N0-BER曲线')
    
    # 绘制原始数据点
    colors = ['red', 'green', 'orange', 'purple']
    for i, (snr, ber, dur, color) in enumerate(zip(es_n0, ber_values, durations, colors)):
        plt.semilogy(snr, ber, 's', markersize=12, color=color,
                    markeredgecolor='black', markeredgewidth=2)
        plt.annotate(f'{dur}s', xy=(snr, ber), xytext=(5, 5), 
                    textcoords='offset points', fontsize=11, fontweight='bold')
    
    plt.xlabel('Es/N0 (dB)', fontsize=16, fontweight='bold')
    plt.ylabel('误码率 (BER)', fontsize=16, fontweight='bold')
    plt.title('Es/N0 vs BER 关系曲线\n(动态信道下的非单调性)', fontsize=18, fontweight='bold')
    plt.grid(True, alpha=0.3)
    plt.legend(fontsize=12)
    
    # 添加说明
    plt.text(0.02, 0.98, 
             '注意:\n• 更高的Es/N0不一定\n  意味着更低的BER\n• 信道老化效应的影响', 
             transform=plt.gca().transAxes, fontsize=12, verticalalignment='top',
             bbox=dict(boxstyle='round,pad=0.5', facecolor='lightyellow', alpha=0.9))
    
    plt.tight_layout()
    plt.savefig('json6_es_n0_ber_relationship.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("Es/N0-BER关系曲线已生成：json6_es_n0_ber_relationship.png")

def plot_dual_axis_analysis():
    """绘制双轴分析图：Es/N0和BER随符号时间的变化"""
    durations, es_n0, ber_values, analyses = extract_performance_data()
    
    # 创建密集的插值点
    duration_smooth = np.linspace(0.1, 1.5, 500)
    
    # Es/N0插值（线性增长）
    cs_snr = CubicSpline(durations, es_n0)
    es_n0_smooth = cs_snr(duration_smooth)
    
    # BER插值（非单调）
    cs_ber = CubicSpline(durations, np.log10(ber_values))
    ber_smooth = 10**cs_ber(duration_smooth)
    
    # 创建双轴图
    fig, ax1 = plt.subplots(figsize=(14, 8))
    
    # 左轴：Es/N0
    color1 = 'tab:blue'
    ax1.set_xlabel('符号持续时间 (s)', fontsize=16, fontweight='bold')
    ax1.set_ylabel('Es/N0 (dB)', color=color1, fontsize=16, fontweight='bold')
    line1 = ax1.plot(duration_smooth, es_n0_smooth, color=color1, linewidth=4, 
                     alpha=0.8, label='Es/N0 (能量增益)')
    ax1.plot(durations, es_n0, 'o', color=color1, markersize=10, 
             markeredgecolor='black', markeredgewidth=2)
    ax1.tick_params(axis='y', labelcolor=color1, labelsize=12)
    ax1.grid(True, alpha=0.3)
    
    # 右轴：BER
    ax2 = ax1.twinx()
    color2 = 'tab:red'
    ax2.set_ylabel('误码率 (BER)', color=color2, fontsize=16, fontweight='bold')
    line2 = ax2.semilogy(duration_smooth, ber_smooth, color=color2, linewidth=4, 
                         alpha=0.8, label='BER (综合性能)')
    ax2.semilogy(durations, ber_values, 's', color=color2, markersize=10,
                 markeredgecolor='black', markeredgewidth=2)
    ax2.tick_params(axis='y', labelcolor=color2, labelsize=12)
    
    # 标注信道相干时间
    ax1.axvline(x=0.8, color='green', linestyle='--', linewidth=3, alpha=0.8,
                label='信道相干时间 Tc=0.8s')
    
    # 标注最优点
    optimal_idx = np.argmin(ber_values)
    ax1.annotate('最优平衡点', xy=(durations[optimal_idx], es_n0[optimal_idx]),
                xytext=(0.7, 8), fontsize=14, fontweight='bold', color='darkgreen',
                arrowprops=dict(arrowstyle='->', color='darkgreen', lw=2),
                bbox=dict(boxstyle='round,pad=0.3', facecolor='lightgreen', alpha=0.8))
    
    # 添加图例
    lines = line1 + line2 + [plt.Line2D([0], [0], color='green', linestyle='--', linewidth=3)]
    labels = ['Es/N0 (能量增益)', 'BER (综合性能)', '信道相干时间']
    ax1.legend(lines, labels, loc='center right', fontsize=12)
    plt.tight_layout()
    plt.savefig('json6_dual_axis_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("双轴分析图已生成：json6_dual_axis_analysis.png")

def print_interpolation_summary():
    """打印插值分析总结"""
    durations, es_n0, ber_values, analyses = extract_performance_data()
    
    print("=" * 70)
    print("6.json 连续插值分析总结")
    print("=" * 70)
    
    print(f"\n📊 原始离散数据点:")
    for dur, snr, ber, analysis in zip(durations, es_n0, ber_values, analyses):
        print(f"   • {dur}s: Es/N0={snr}dB, BER={ber:.1e} - {analysis}")
    
    print(f"\n🔄 插值处理:")
    print(f"   • 插值方法: 三次样条插值")
    print(f"   • 原始点数: {len(durations)}个")
    print(f"   • 插值点数: 500个")
    print(f"   • 平滑度: 高")
    
    print(f"\n🎯 关键发现:")
    print(f"   • 0.5s是真正的最优点 (黄金平衡点)")
    print(f"   • Es/N0单调增长，但BER非单调")
    print(f"   • 信道相干时间Tc=0.8s是关键分界点")
    print(f"   • 信道老化效应在长符号时占主导")
    
    print(f"\n💡 连续曲线的价值:")
    print(f"   • 揭示了离散点之间的平滑过渡")
    print(f"   • 更好地理解性能变化趋势")
    print(f"   • 为实际系统设计提供精确指导")

def estimate_snr_values(durations, es_n0_values, ber_values):
    """基于Es/N0和BER推断SNR值"""
    # 假设噪声功率密度恒定，SNR与Es/N0的关系取决于符号速率
    # SNR = Es/N0 + 10*log10(符号速率) - 常数
    # 这里我们基于实际通信系统的经验进行合理推断

    snr_values = []
    for dur, es_n0, ber in zip(durations, es_n0_values, ber_values):
        # 基于符号持续时间和Es/N0推断SNR
        # 考虑处理增益和带宽效应
        symbol_rate = 1.0 / dur  # 符号速率
        processing_gain = 10 * np.log10(symbol_rate / 1.0)  # 相对于1Hz的处理增益

        # SNR通常比Es/N0低，因为Es/N0是每符号能量与噪声功率密度的比值
        # 而SNR是信号功率与噪声功率的比值
        snr = es_n0 - processing_gain - 3  # 经验调整
        snr_values.append(snr)

    return np.array(snr_values)

def plot_snr_ber_duration_analysis():
    """绘制SNR、BER、符号持续时间三参数分析图"""
    durations, es_n0, ber_values, analyses = extract_performance_data()

    # 推断SNR值
    snr_values = estimate_snr_values(durations, es_n0, ber_values)

    # 创建密集的插值点
    duration_smooth = np.linspace(0.1, 1.5, 500)

    # SNR插值
    cs_snr = CubicSpline(durations, snr_values)
    snr_smooth = cs_snr(duration_smooth)

    # BER插值（对数空间）
    cs_ber = CubicSpline(durations, np.log10(ber_values))
    ber_smooth = 10**cs_ber(duration_smooth)

    # 创建双轴图
    fig, ax1 = plt.subplots(figsize=(16, 10))

    # 左轴：SNR
    color1 = '#2E8B57'  # 海绿色
    ax1.set_xlabel('符号持续时间 (s)', fontsize=16, fontweight='bold')
    ax1.set_ylabel('信噪比 SNR (dB)', color=color1, fontsize=16, fontweight='bold')

    # 绘制SNR连续曲线
    line1 = ax1.plot(duration_smooth, snr_smooth, color=color1, linewidth=4,
                     alpha=0.8, label='SNR连续曲线')

    # 绘制SNR原始数据点
    snr_points = ax1.plot(durations, snr_values, 'o', color=color1, markersize=12,
                         markerfacecolor='lightgreen', markeredgecolor=color1,
                         markeredgewidth=3, label='SNR数据点')

    ax1.tick_params(axis='y', labelcolor=color1, labelsize=12)
    ax1.grid(True, alpha=0.3)
    ax1.set_ylim(-2, 12)

    # 右轴：BER
    ax2 = ax1.twinx()
    color2 = '#DC143C'  # 深红色
    ax2.set_ylabel('误码率 BER', color=color2, fontsize=16, fontweight='bold')

    # 绘制BER连续曲线
    line2 = ax2.semilogy(duration_smooth, ber_smooth, color=color2, linewidth=4,
                         alpha=0.8, label='BER连续曲线')

    # 绘制BER原始数据点
    ber_points = ax2.semilogy(durations, ber_values, 's', color=color2, markersize=12,
                             markerfacecolor='lightcoral', markeredgecolor=color2,
                             markeredgewidth=3, label='BER数据点')

    ax2.tick_params(axis='y', labelcolor=color2, labelsize=12)
    ax2.set_ylim(1e-6, 1e-1)

    # 添加数据点标注
    for i, (dur, snr, ber) in enumerate(zip(durations, snr_values, ber_values)):
        # SNR标注
        ax1.annotate(f'{snr:.1f}dB', xy=(dur, snr), xytext=(5, 15),
                    textcoords='offset points', fontsize=11, fontweight='bold',
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='lightgreen', alpha=0.8),
                    ha='center')

        # BER标注
        ax2.annotate(f'{ber:.1e}', xy=(dur, ber), xytext=(5, -20),
                    textcoords='offset points', fontsize=11, fontweight='bold',
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='lightcoral', alpha=0.8),
                    ha='center')

        # 符号时间标注
        ax1.annotate(f'{dur}s', xy=(dur, snr), xytext=(0, -30),
                    textcoords='offset points', fontsize=12, fontweight='bold',
                    ha='center', va='top')

    # 标注信道相干时间
    coherence_line = ax1.axvline(x=0.8, color='purple', linestyle='--', linewidth=3, alpha=0.8)

    # 添加性能区域
    ax1.fill_betweenx(ax1.get_ylim(), 0, 0.8, alpha=0.15, color='green', label='信道稳定区域')
    ax1.fill_betweenx(ax1.get_ylim(), 0.8, 1.5, alpha=0.15, color='red', label='信道老化区域')

    # 标注最优点
    optimal_idx = np.argmin(ber_values)
    ax1.annotate('最优工作点\n(SNR-BER平衡)',
                xy=(durations[optimal_idx], snr_values[optimal_idx]),
                xytext=(0.3, 8), fontsize=14, fontweight='bold', color='darkblue',
                arrowprops=dict(arrowstyle='->', color='darkblue', lw=3),
                bbox=dict(boxstyle='round,pad=0.5', facecolor='lightyellow', alpha=0.9))

    # 创建综合图例
    lines1 = line1 + snr_points
    lines2 = line2 + ber_points
    coherence_legend = [plt.Line2D([0], [0], color='purple', linestyle='--', linewidth=3)]

    # 分别添加图例
    legend1 = ax1.legend(lines1 + coherence_legend,
                        ['SNR连续曲线', 'SNR数据点', '信道相干时间 Tc=0.8s'],
                        loc='upper left', fontsize=11)
    legend2 = ax2.legend(lines2, ['BER连续曲线', 'BER数据点'],
                        loc='upper right', fontsize=11)

    # 添加说明文本
    ax1.text(0.02, 0.02,
             'SNR推断依据:\n• 基于Es/N0和符号速率\n• 考虑处理增益效应\n• 符合实际系统特性',
             transform=ax1.transAxes, fontsize=11, verticalalignment='bottom',
             bbox=dict(boxstyle='round,pad=0.5', facecolor='lightblue', alpha=0.9))

    plt.title('SNR-BER-符号持续时间 三参数关系分析\n(连续插值曲线)', fontsize=18, fontweight='bold')
    plt.tight_layout()
    plt.savefig('json6_snr_ber_duration_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()  # 关闭图表而不显示

    print("SNR-BER-符号持续时间分析图已生成：json6_snr_ber_duration_analysis.png")

    # 打印推断的SNR值
    print(f"\n推断的SNR值:")
    for dur, snr, es_n0 in zip(durations, snr_values, es_n0):
        print(f"   • {dur}s: SNR={snr:.1f}dB (Es/N0={es_n0}dB)")

    return snr_values

if __name__ == "__main__":
    print("正在生成6.json的连续插值分析...")

    # 生成连续BER曲线
    plot_continuous_ber_curve()

    # 生成Es/N0 vs BER关系曲线
    plot_es_n0_vs_ber_relationship()

    # 生成双轴分析图
    plot_dual_axis_analysis()

    # 生成SNR-BER-符号持续时间三参数分析图
    plot_snr_ber_duration_analysis()

    # 打印分析总结
    print_interpolation_summary()

    print("\n生成的图片:")
    print("- json6_continuous_ber_curve.png: 连续BER性能曲线")
    print("- json6_es_n0_ber_relationship.png: Es/N0-BER关系曲线")
    print("- json6_dual_axis_analysis.png: 双轴动态平衡分析")
    print("- json6_snr_ber_duration_analysis.png: SNR-BER-符号持续时间三参数分析")
