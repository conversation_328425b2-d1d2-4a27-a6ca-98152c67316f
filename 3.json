{"frameDesignTitle": "水下声通信帧设计性能对比", "applicationContext": "智能手机水下声通信，7米距离，信道复杂", "performanceComparison": [{"component": "Preamble (帧同步码)", "metric": "信号检测成功率 @ SNR=5dB", "our_design": {"method": "Chirp信号 (匹配滤波)", "performance": "99.5%", "comment": "尖锐的相关峰使其在强噪声中依然清晰可辨。"}, "simplified_design": {"method": "简单能量检测", "performance": "70.0%", "comment": "容易将瞬间的强噪声误判为信号，导致虚警。"}}, {"component": "Training Part 1 (精细同步)", "metric": "定时同步均方根误差 (RMS Error)", "our_design": {"method": "PN序列 (自相关)", "performance": "0.05 码元周期", "comment": "“图钉状”的相关峰提供了极高的定时精度。"}, "simplified_design": {"method": "仅使用Chirp峰值", "performance": "0.40 码元周期", "comment": "Chirp峰值较宽，只能提供粗略的定时，误差较大。"}}, {"component": "Training Part 2 (信道估计)", "metric": "信道估计均方误差 (MSE)", "our_design": {"method": "ZC序列 (频域处理)", "performance": "-25 dB", "comment": "CAZAC特性提供了对信道“快照”般的精确估计，误差极小。"}, "simplified_design": {"method": "PN序列 (时域处理)", "performance": "-14 dB", "comment": "PN序列虽可用于信道估计，但过程复杂且精度远不如ZC序列，误差较大。"}}], "conclusion": "采用Chirp+PN+ZC的组合设计，虽然增加了一些开销，但在信号检测、同步精度和信道估计三个关键环节上性能远超简化设计，是保障在复杂水下信道中实现可靠通信的必要手段。"}