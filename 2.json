{"scenarioDetails": {"application": "智能手机水下无线声通信", "distance_meters": 7, "channelDescription": "真实水下声学信道（例如游泳池或湖泊），存在严重的多径和衰减效应"}, "plotData": {"title": "信噪比(SNR) vs. 误码率(BER) 在7米水下距离", "xAxis": {"label": "信噪比 (SNR)", "unit": "dB"}, "yAxis": {"label": "误码率 (BER)", "unit": "<PERSON><PERSON>", "scale": "log"}, "dataPoints": [{"snr_dB": 5, "ber": 0.22, "description": "信号微弱，连接极不可靠"}, {"snr_dB": 8, "ber": 0.051, "description": "连接开始建立，但丢包率非常高"}, {"snr_dB": 12, "ber": 0.0012, "description": "性能出现拐点，可用于传输简单指令或传感器数据"}, {"snr_dB": 16, "ber": 0.00025, "description": "连接变得较为可靠，可传输少量压缩数据"}, {"snr_dB": 20, "ber": 5.5e-05, "description": "性能提升开始减缓，接近该信道下的错误平层"}, {"snr_dB": 24, "ber": 3.2e-05, "description": "错误平层效应明显，进一步增加信噪比带来的收益有限"}]}}